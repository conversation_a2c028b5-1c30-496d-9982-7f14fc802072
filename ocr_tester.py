"""
Main OCR Testing Framework
"""
import os
import json
import csv
import logging
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd
from tqdm import tqdm

from config import (
    OCR_MODELS, OUTPUT_FORMATS, IMAGE_PREPROCESSING, 
    METRICS, INPUT_DIR, OUTPUT_DIR, SUPPORTED_EXTENSIONS
)
from ocr_models import get_available_models
from utils import preprocess_image, calculate_accuracy_metrics, save_results, create_comparison_report

logger = logging.getLogger(__name__)

class OCRTester:
    """Main class for testing multiple OCR models"""
    
    def __init__(self, config_override: Dict = None):
        """
        Initialize OCR Tester
        
        Args:
            config_override: Optional configuration overrides
        """
        self.config = OCR_MODELS.copy()
        if config_override:
            self.config.update(config_override)
        
        self.models = get_available_models(self.config)
        self.results = []
        
        logger.info(f"Initialized OCR Tester with {len(self.models)} models: {list(self.models.keys())}")
    
    def test_single_image(self, image_path: str, ground_truth: str = None) -> List[Dict[str, Any]]:
        """
        Test a single image with all available OCR models
        
        Args:
            image_path: Path to the image file
            ground_truth: Optional ground truth text for accuracy calculation
            
        Returns:
            List of results from all models
        """
        image_results = []
        
        logger.info(f"Testing image: {image_path}")
        
        # Preprocess image if enabled
        try:
            if any(IMAGE_PREPROCESSING.values()):
                preprocessed_image_path = self._preprocess_and_save(image_path)
            else:
                preprocessed_image_path = image_path
        except Exception as e:
            logger.error(f"Error preprocessing {image_path}: {str(e)}")
            preprocessed_image_path = image_path
        
        # Test with each model
        for model_name, model in self.models.items():
            logger.info(f"Testing with {model_name}...")
            
            try:
                result = model.process_image(preprocessed_image_path)
                
                # Calculate accuracy metrics
                if METRICS:
                    metrics = calculate_accuracy_metrics(
                        result['extracted_text'], 
                        ground_truth
                    )
                    result['metrics'] = metrics
                
                # Add ground truth if provided
                if ground_truth:
                    result['ground_truth'] = ground_truth
                
                image_results.append(result)
                
            except Exception as e:
                logger.error(f"Error testing {model_name} on {image_path}: {str(e)}")
                error_result = {
                    'model_name': model_name,
                    'image_path': image_path,
                    'extracted_text': '',
                    'confidence_score': 0.0,
                    'processing_time': 0.0,
                    'success': False,
                    'error': str(e)
                }
                image_results.append(error_result)
        
        return image_results
    
    def test_directory(self, input_dir: str = None, ground_truth_file: str = None) -> List[Dict[str, Any]]:
        """
        Test all images in a directory
        
        Args:
            input_dir: Directory containing images (defaults to INPUT_DIR)
            ground_truth_file: Optional JSON file with ground truth texts
            
        Returns:
            List of all results
        """
        if input_dir is None:
            input_dir = INPUT_DIR
        
        input_path = Path(input_dir)
        if not input_path.exists():
            raise ValueError(f"Input directory does not exist: {input_dir}")
        
        # Load ground truth if provided
        ground_truths = {}
        if ground_truth_file and Path(ground_truth_file).exists():
            with open(ground_truth_file, 'r', encoding='utf-8') as f:
                ground_truths = json.load(f)
        
        # Find all supported image files
        image_files = []
        for ext in SUPPORTED_EXTENSIONS:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.warning(f"No supported image files found in {input_dir}")
            return []
        
        logger.info(f"Found {len(image_files)} images to process")
        
        all_results = []
        
        # Process each image
        for image_file in tqdm(image_files, desc="Processing images"):
            image_name = image_file.name
            ground_truth = ground_truths.get(image_name, None)
            
            image_results = self.test_single_image(str(image_file), ground_truth)
            all_results.extend(image_results)
        
        self.results = all_results
        return all_results
    
    def save_results(self, output_dir: str = None, filename_prefix: str = "ocr_results"):
        """
        Save results in multiple formats
        
        Args:
            output_dir: Output directory (defaults to OUTPUT_DIR)
            filename_prefix: Prefix for output files
        """
        if output_dir is None:
            output_dir = OUTPUT_DIR
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        if not self.results:
            logger.warning("No results to save")
            return
        
        # Save detailed JSON results
        if OUTPUT_FORMATS.get('json', True):
            json_file = output_path / f"{filename_prefix}_detailed.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            logger.info(f"Detailed results saved to {json_file}")
        
        # Save text files for each image and model
        if OUTPUT_FORMATS.get('text', True):
            text_dir = output_path / "text_outputs"
            text_dir.mkdir(exist_ok=True)
            
            for result in self.results:
                if result['success'] and result['extracted_text']:
                    image_name = Path(result['image_path']).stem
                    model_name = result['model_name']
                    text_file = text_dir / f"{image_name}_{model_name}.txt"
                    
                    with open(text_file, 'w', encoding='utf-8') as f:
                        f.write(result['extracted_text'])
        
        # Save CSV summary
        if OUTPUT_FORMATS.get('csv', True):
            self._save_csv_summary(output_path, filename_prefix)
        
        # Save comparison report
        if OUTPUT_FORMATS.get('detailed_report', True):
            self._save_comparison_report(output_path, filename_prefix)
    
    def _preprocess_and_save(self, image_path: str) -> str:
        """Preprocess image and save to temp location"""
        import cv2
        
        preprocessed_image = preprocess_image(image_path, IMAGE_PREPROCESSING)
        
        # Save preprocessed image
        temp_dir = OUTPUT_DIR / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        image_name = Path(image_path).name
        temp_path = temp_dir / f"preprocessed_{image_name}"
        
        cv2.imwrite(str(temp_path), preprocessed_image)
        return str(temp_path)
    
    def _save_csv_summary(self, output_path: Path, filename_prefix: str):
        """Save results summary as CSV"""
        csv_data = []
        
        for result in self.results:
            row = {
                'image_path': Path(result['image_path']).name,
                'model_name': result['model_name'],
                'success': result['success'],
                'processing_time': result.get('processing_time', 0),
                'confidence_score': result.get('confidence_score', 0),
                'text_length': len(result.get('extracted_text', '')),
                'word_count': len(result.get('extracted_text', '').split()),
                'error': result.get('error', '')
            }
            
            # Add metrics if available
            if 'metrics' in result:
                row.update(result['metrics'])
            
            csv_data.append(row)
        
        csv_file = output_path / f"{filename_prefix}_summary.csv"
        df = pd.DataFrame(csv_data)
        df.to_csv(csv_file, index=False)
        logger.info(f"CSV summary saved to {csv_file}")
    
    def _save_comparison_report(self, output_path: Path, filename_prefix: str):
        """Save detailed comparison report"""
        report = create_comparison_report(self.results)
        
        report_file = output_path / f"{filename_prefix}_comparison_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Comparison report saved to {report_file}")
    
    def print_summary(self):
        """Print a summary of results to console"""
        if not self.results:
            print("No results available")
            return
        
        print("\n" + "="*60)
        print("OCR TESTING SUMMARY")
        print("="*60)
        
        # Group results by model
        model_stats = {}
        for result in self.results:
            model_name = result['model_name']
            if model_name not in model_stats:
                model_stats[model_name] = {
                    'total': 0,
                    'successful': 0,
                    'total_time': 0,
                    'total_confidence': 0,
                    'total_text_length': 0
                }
            
            stats = model_stats[model_name]
            stats['total'] += 1
            
            if result['success']:
                stats['successful'] += 1
                stats['total_time'] += result.get('processing_time', 0)
                stats['total_confidence'] += result.get('confidence_score', 0)
                stats['total_text_length'] += len(result.get('extracted_text', ''))
        
        # Print statistics for each model
        for model_name, stats in model_stats.items():
            print(f"\n{model_name.upper()}:")
            print(f"  Success Rate: {stats['successful']}/{stats['total']} ({stats['successful']/stats['total']*100:.1f}%)")
            
            if stats['successful'] > 0:
                avg_time = stats['total_time'] / stats['successful']
                avg_confidence = stats['total_confidence'] / stats['successful']
                avg_text_length = stats['total_text_length'] / stats['successful']
                
                print(f"  Average Processing Time: {avg_time:.2f}s")
                print(f"  Average Confidence: {avg_confidence:.2f}")
                print(f"  Average Text Length: {avg_text_length:.0f} characters")
        
        print("\n" + "="*60)
