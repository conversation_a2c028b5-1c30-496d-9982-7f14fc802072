"""
Setup script for OCR testing environment
"""
import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False
    return True

def setup_directories():
    """Create necessary directories"""
    print("Setting up directories...")
    
    directories = [
        "test_images",
        "results",
        "results/text_outputs",
        "results/visualizations",
        "results/temp",
        "models"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✓ Created directory: {dir_name}")

def download_sample_images():
    """Download or create sample test images"""
    print("Setting up sample test images...")
    
    sample_dir = Path("test_images")
    
    # Create a simple text image for testing
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # Create a simple handwritten-style text image
        img = Image.new('RGB', (800, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font
        try:
            font = ImageFont.truetype("arial.ttf", 40)
        except:
            font = ImageFont.load_default()
        
        text = "Hello World! This is a test for OCR."
        draw.text((50, 80), text, fill='black', font=font)
        
        sample_file = sample_dir / "sample_text.png"
        img.save(sample_file)
        print(f"✓ Created sample image: {sample_file}")
        
        # Create ground truth file
        ground_truth = {
            "sample_text.png": text
        }
        
        gt_file = sample_dir / "ground_truth.json"
        import json
        with open(gt_file, 'w') as f:
            json.dump(ground_truth, f, indent=2)
        print(f"✓ Created ground truth file: {gt_file}")
        
    except ImportError:
        print("⚠ PIL not available yet. Sample images will be created after installation.")

def check_system_dependencies():
    """Check for system-level dependencies"""
    print("Checking system dependencies...")
    
    # Check for Tesseract
    try:
        subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
        print("✓ Tesseract OCR found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠ Tesseract OCR not found. Please install it:")
        print("  Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
        print("  macOS: brew install tesseract")
        print("  Ubuntu: sudo apt-get install tesseract-ocr")
    
    # Check for CUDA (optional)
    try:
        import torch
        if torch.cuda.is_available():
            print("✓ CUDA available for GPU acceleration")
        else:
            print("⚠ CUDA not available. Models will run on CPU (slower)")
    except ImportError:
        print("⚠ PyTorch not installed yet")

def create_example_config():
    """Create an example configuration file"""
    print("Creating example configuration...")
    
    example_config = '''"""
Example configuration for specific use cases
"""

# Configuration for CPU-only testing (no GPU)
CPU_ONLY_CONFIG = {
    'tesseract': {
        'enabled': True,
        'config': '--oem 3 --psm 6'
    },
    'easyocr': {
        'enabled': True,
        'languages': ['en'],
        'gpu': False
    },
    'paddleocr': {
        'enabled': False,  # Disable if causing issues
        'lang': 'en',
        'use_gpu': False
    },
    'trocr': {
        'enabled': True,
        'model_name': 'microsoft/trocr-base-handwritten',
        'device': 'cpu'
    }
}

# Configuration for handwritten text focus
HANDWRITTEN_CONFIG = {
    'tesseract': {
        'enabled': True,
        'config': '--oem 3 --psm 8'  # Single word mode for handwriting
    },
    'easyocr': {
        'enabled': True,
        'languages': ['en'],
        'gpu': True
    },
    'paddleocr': {
        'enabled': True,
        'lang': 'en',
        'use_gpu': True
    },
    'trocr': {
        'enabled': True,
        'model_name': 'microsoft/trocr-base-handwritten',
        'device': 'cuda'
    }
}

# Configuration for printed text focus
PRINTED_TEXT_CONFIG = {
    'tesseract': {
        'enabled': True,
        'config': '--oem 3 --psm 6'
    },
    'easyocr': {
        'enabled': True,
        'languages': ['en'],
        'gpu': True
    },
    'paddleocr': {
        'enabled': True,
        'lang': 'en',
        'use_gpu': True
    },
    'trocr': {
        'enabled': False  # TrOCR is specialized for handwriting
    }
}
'''
    
    with open("example_configs.py", "w") as f:
        f.write(example_config)
    print("✓ Created example_configs.py")

def main():
    print("="*60)
    print("OCR TESTING ENVIRONMENT SETUP")
    print("="*60)
    
    # Check system dependencies first
    check_system_dependencies()
    print()
    
    # Setup directories
    setup_directories()
    print()
    
    # Install requirements
    if install_requirements():
        print()
        
        # Create sample images after PIL is available
        download_sample_images()
        print()
        
        # Create example configuration
        create_example_config()
        print()
        
        print("="*60)
        print("SETUP COMPLETE!")
        print("="*60)
        print()
        print("Next steps:")
        print("1. Add your test images to the 'test_images' directory")
        print("2. Run: python run_ocr_tests.py")
        print("3. Check results in the 'results' directory")
        print()
        print("For specific configurations, see example_configs.py")
        print()
        print("Example commands:")
        print("  # Test all images with all models:")
        print("  python run_ocr_tests.py")
        print()
        print("  # Test specific models only:")
        print("  python run_ocr_tests.py --models tesseract easyocr")
        print()
        print("  # Test a single image:")
        print("  python run_ocr_tests.py --single-image test_images/sample_text.png")
        print()
        print("  # Use ground truth for accuracy measurement:")
        print("  python run_ocr_tests.py --ground-truth test_images/ground_truth.json")
        
    else:
        print("Setup failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
