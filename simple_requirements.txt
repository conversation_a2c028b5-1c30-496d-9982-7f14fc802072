# Simple OCR Requirements
# Basic packages for simple OCR testing

# Core OCR libraries
pytesseract==0.3.10
easyocr==1.7.1

# Optional advanced OCR models (install as needed)
# paddleocr  # Uncomment to enable PaddleOCR
# transformers  # Uncomment to enable TrOCR
# torch  # Required for TrOCR

# Image processing
opencv-python==4.9.0.80
Pillow==10.1.0
numpy==1.24.4

# Configuration management
python-dotenv==1.0.0

# Basic utilities (already included with Python)
# json, time, pathlib, os are built-in
