# OCR Model Testing Framework

A comprehensive framework for testing and comparing multiple OCR (Optical Character Recognition) models, with a focus on handwritten text recognition. This tool helps you find the best OCR model for your specific use case by testing multiple models and providing detailed performance analysis.

## Features

- **Multiple OCR Models**: Test with Tesseract, EasyOCR, PaddleOCR, and TrOCR
- **Handwritten Text Focus**: Specialized support for handwritten text recognition
- **Comprehensive Analysis**: Detailed performance metrics and accuracy measurements
- **Multiple Output Formats**: Results in JSON, text files, and CSV
- **Visualization**: Automatic generation of performance comparison charts
- **Batch Processing**: Test multiple images at once
- **Ground Truth Comparison**: Accuracy measurement when ground truth is available

## Supported OCR Models

1. **Tesseract OCR** - Traditional OCR engine, good for printed text
2. **EasyOCR** - Deep learning-based OCR with good multilingual support
3. **PaddleOCR** - High-performance OCR with excellent accuracy
4. **TrOCR** - Transformer-based OCR specialized for handwritten text

## Installation

### 1. System Dependencies

**Tesseract OCR** (required):
- Windows: Download from [UB-Mannheim/tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
- macOS: `brew install tesseract`
- Ubuntu: `sudo apt-get install tesseract-ocr`

### 2. Python Setup

```bash
# Clone or download this repository
cd ocr-testing-framework

# Run the setup script
python setup.py
```

The setup script will:
- Install all required Python packages
- Create necessary directories
- Generate sample test images
- Create example configurations

### 3. Manual Installation (Alternative)

```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Add Test Images

Place your test images in the `test_images` directory. Supported formats:
- JPG, JPEG, PNG, BMP, TIFF, TIF

### 2. Run Basic Test

```bash
# Test all images with all available models
python run_ocr_tests.py

# Test with specific models only
python run_ocr_tests.py --models tesseract easyocr trocr

# Test a single image
python run_ocr_tests.py --single-image test_images/your_image.jpg
```

### 3. View Results

Results are saved in the `results` directory:
- `ocr_results_detailed.json` - Complete results with all metadata
- `ocr_results_summary.csv` - Summary statistics
- `text_outputs/` - Individual text files for each model/image combination
- `analysis_report.json` - Comprehensive performance analysis
- `visualizations/` - Performance comparison charts

## Advanced Usage

### Ground Truth Comparison

For accuracy measurement, create a ground truth JSON file:

```json
{
  "image1.jpg": "Expected text content for image1",
  "image2.jpg": "Expected text content for image2"
}
```

Then run with ground truth:

```bash
python run_ocr_tests.py --ground-truth test_images/ground_truth.json
```

### Custom Configuration

Modify `config.py` or create custom configurations:

```python
from ocr_tester import OCRTester

# Custom configuration for CPU-only testing
config = {
    'tesseract': {'enabled': True, 'config': '--oem 3 --psm 6'},
    'easyocr': {'enabled': True, 'languages': ['en'], 'gpu': False},
    'trocr': {'enabled': True, 'device': 'cpu'}
}

tester = OCRTester(config)
results = tester.test_directory('my_images')
```

### Programmatic Usage

```python
from ocr_tester import OCRTester
from metrics_analyzer import OCRMetricsAnalyzer

# Initialize tester
tester = OCRTester()

# Test single image
results = tester.test_single_image('path/to/image.jpg')

# Test directory
results = tester.test_directory('path/to/images')

# Save results
tester.save_results('output_directory')

# Analyze results
analyzer = OCRMetricsAnalyzer(results_data=results)
report = analyzer.generate_performance_report()
analyzer.create_visualizations('viz_output')
```

## Configuration Options

### Model Settings

Each model can be configured in `config.py`:

```python
OCR_MODELS = {
    'tesseract': {
        'enabled': True,
        'config': '--oem 3 --psm 6'  # OCR Engine Mode and Page Segmentation
    },
    'easyocr': {
        'enabled': True,
        'languages': ['en'],
        'gpu': True  # Set to False for CPU-only
    },
    'paddleocr': {
        'enabled': True,
        'lang': 'en',
        'use_gpu': True
    },
    'trocr': {
        'enabled': True,
        'model_name': 'microsoft/trocr-base-handwritten',
        'device': 'cuda'  # 'cpu' for CPU-only
    }
}
```

### Image Preprocessing

```python
IMAGE_PREPROCESSING = {
    'resize': True,
    'max_width': 2000,
    'max_height': 2000,
    'enhance_contrast': True,
    'denoise': True,
    'binarize': False  # For very poor quality images
}
```

## Output Formats

### JSON Results
Complete results with metadata, confidence scores, and processing times.

### Text Files
Individual `.txt` files for each model/image combination containing extracted text.

### CSV Summary
Tabular data suitable for spreadsheet analysis.

### Analysis Report
Comprehensive performance analysis with recommendations.

### Visualizations
- Success rate comparison
- Processing time distributions
- Confidence score analysis
- Accuracy metrics (when ground truth available)

## Performance Metrics

The framework measures:

- **Success Rate**: Percentage of successful text extractions
- **Processing Time**: Time taken for each extraction
- **Confidence Scores**: Model confidence in results
- **Text Length**: Amount of text extracted
- **Accuracy Metrics**: When ground truth is available
  - Character-level accuracy
  - Word-level accuracy
  - Levenshtein distance

## Troubleshooting

### Common Issues

1. **Tesseract not found**: Install Tesseract OCR system dependency
2. **CUDA errors**: Set `gpu: False` in model configurations for CPU-only
3. **Memory issues**: Reduce image sizes or disable GPU for some models
4. **Import errors**: Run `python setup.py` to install dependencies

### GPU vs CPU

- **GPU**: Faster processing for EasyOCR, PaddleOCR, and TrOCR
- **CPU**: Slower but works on all systems

To force CPU-only mode, set in `config.py`:
```python
'gpu': False  # for EasyOCR
'use_gpu': False  # for PaddleOCR
'device': 'cpu'  # for TrOCR
```

## Best Practices

1. **Image Quality**: Higher resolution images generally produce better results
2. **Preprocessing**: Enable image enhancement for poor quality images
3. **Model Selection**: 
   - Use TrOCR for handwritten text
   - Use Tesseract for clean printed text
   - Use EasyOCR for general purpose
4. **Ground Truth**: Provide ground truth for accurate performance measurement
5. **Batch Testing**: Test multiple images to get reliable performance statistics

## Contributing

Feel free to contribute by:
- Adding new OCR models
- Improving preprocessing techniques
- Adding new metrics
- Enhancing visualizations

## License

This project is open source. Please check individual model licenses for commercial use.
