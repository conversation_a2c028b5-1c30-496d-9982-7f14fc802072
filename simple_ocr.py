#!/usr/bin/env python3
"""
Simple OCR Testing Script
Basic OCR functionality to extract text and save to .txt and .json files
"""

import os
import json
import time
from pathlib import Path
from PIL import Image
import cv2
import numpy as np

# Create directories
def setup_directories():
    """Create necessary directories"""
    directories = ["test_images", "results", "results/text_files", "results/json_files"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ Directories created")

class SimpleOCR:
    """Simple OCR class for basic text extraction"""
    
    def __init__(self):
        self.results = []
        print("Initializing Simple OCR...")
        
    def extract_text_tesseract(self, image_path):
        """Extract text using Tesseract OCR"""
        try:
            import pytesseract
            
            # Load and preprocess image
            image = cv2.imread(image_path)
            if image is None:
                return "", 0, "Could not load image"
            
            # Convert to grayscale for better OCR
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply some basic preprocessing
            # Resize if image is too small
            height, width = gray.shape
            if width < 300:
                scale = 300 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Enhance contrast
            gray = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
            
            # Extract text
            start_time = time.time()
            extracted_text = pytesseract.image_to_string(gray, config='--oem 3 --psm 6')
            processing_time = time.time() - start_time
            
            # Get confidence score
            try:
                data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            except:
                avg_confidence = 0
            
            return extracted_text.strip(), processing_time, avg_confidence, None
            
        except ImportError:
            return "", 0, 0, "pytesseract not installed. Install with: pip install pytesseract"
        except Exception as e:
            return "", 0, 0, str(e)
    
    def extract_text_easyocr(self, image_path):
        """Extract text using EasyOCR"""
        try:
            import easyocr
            
            # Initialize reader (this might take time on first run)
            if not hasattr(self, 'easyocr_reader'):
                print("Initializing EasyOCR (this may take a moment)...")
                self.easyocr_reader = easyocr.Reader(['en'], gpu=False)  # Use CPU for simplicity
            
            start_time = time.time()
            results = self.easyocr_reader.readtext(image_path)
            processing_time = time.time() - start_time
            
            if not results:
                return "", processing_time, 0, None
            
            # Combine all detected text
            texts = []
            confidences = []
            
            for (bbox, text, confidence) in results:
                texts.append(text)
                confidences.append(confidence)
            
            combined_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return combined_text, processing_time, avg_confidence * 100, None
            
        except ImportError:
            return "", 0, 0, "easyocr not installed. Install with: pip install easyocr"
        except Exception as e:
            return "", 0, 0, str(e)
    
    def process_image(self, image_path, save_results=True):
        """Process a single image with available OCR methods"""
        print(f"\nProcessing: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"Error: Image file not found: {image_path}")
            return None
        
        image_name = Path(image_path).stem
        results = {
            'image_path': image_path,
            'image_name': image_name,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'ocr_results': {}
        }
        
        # Test Tesseract
        print("  Testing Tesseract OCR...")
        text, proc_time, confidence, error = self.extract_text_tesseract(image_path)
        results['ocr_results']['tesseract'] = {
            'extracted_text': text,
            'processing_time': proc_time,
            'confidence_score': confidence,
            'success': error is None,
            'error': error,
            'text_length': len(text),
            'word_count': len(text.split()) if text else 0
        }
        
        if error:
            print(f"    ✗ Tesseract failed: {error}")
        else:
            print(f"    ✓ Tesseract: {len(text)} chars, {confidence:.1f}% confidence, {proc_time:.2f}s")
        
        # Test EasyOCR
        print("  Testing EasyOCR...")
        text, proc_time, confidence, error = self.extract_text_easyocr(image_path)
        results['ocr_results']['easyocr'] = {
            'extracted_text': text,
            'processing_time': proc_time,
            'confidence_score': confidence,
            'success': error is None,
            'error': error,
            'text_length': len(text),
            'word_count': len(text.split()) if text else 0
        }
        
        if error:
            print(f"    ✗ EasyOCR failed: {error}")
        else:
            print(f"    ✓ EasyOCR: {len(text)} chars, {confidence:.1f}% confidence, {proc_time:.2f}s")
        
        # Save results if requested
        if save_results:
            self.save_individual_results(results)
        
        self.results.append(results)
        return results
    
    def save_individual_results(self, result):
        """Save individual image results to text and JSON files"""
        image_name = result['image_name']
        
        # Save text files for each OCR method
        for ocr_method, ocr_data in result['ocr_results'].items():
            if ocr_data['success'] and ocr_data['extracted_text']:
                # Save as .txt file
                txt_file = Path("results/text_files") / f"{image_name}_{ocr_method}.txt"
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(ocr_data['extracted_text'])
                
                print(f"    💾 Saved text: {txt_file}")
        
        # Save complete result as JSON
        json_file = Path("results/json_files") / f"{image_name}_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"    💾 Saved JSON: {json_file}")
    
    def process_directory(self, directory_path="test_images"):
        """Process all images in a directory"""
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"Error: Directory not found: {directory_path}")
            return
        
        # Find image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(directory.glob(f"*{ext}"))
            image_files.extend(directory.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"No image files found in {directory_path}")
            print("Supported formats: JPG, JPEG, PNG, BMP, TIFF, TIF")
            return
        
        print(f"Found {len(image_files)} images to process")
        
        for image_file in image_files:
            self.process_image(str(image_file))
        
        # Save summary
        self.save_summary()
    
    def save_summary(self):
        """Save a summary of all results"""
        if not self.results:
            print("No results to save")
            return
        
        summary = {
            'total_images': len(self.results),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary_by_method': {},
            'detailed_results': self.results
        }
        
        # Calculate summary statistics
        methods = ['tesseract', 'easyocr']
        
        for method in methods:
            successful = 0
            total_time = 0
            total_confidence = 0
            total_text_length = 0
            
            for result in self.results:
                if method in result['ocr_results']:
                    ocr_result = result['ocr_results'][method]
                    if ocr_result['success']:
                        successful += 1
                        total_time += ocr_result['processing_time']
                        total_confidence += ocr_result['confidence_score']
                        total_text_length += ocr_result['text_length']
            
            summary['summary_by_method'][method] = {
                'total_tests': len(self.results),
                'successful_tests': successful,
                'success_rate': successful / len(self.results) if self.results else 0,
                'avg_processing_time': total_time / successful if successful > 0 else 0,
                'avg_confidence': total_confidence / successful if successful > 0 else 0,
                'avg_text_length': total_text_length / successful if successful > 0 else 0
            }
        
        # Save summary
        summary_file = Path("results") / "ocr_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Summary saved: {summary_file}")
        
        # Print summary to console
        self.print_summary(summary)
    
    def print_summary(self, summary):
        """Print summary to console"""
        print("\n" + "="*60)
        print("SIMPLE OCR TESTING SUMMARY")
        print("="*60)
        
        for method, stats in summary['summary_by_method'].items():
            print(f"\n{method.upper()}:")
            print(f"  Success Rate: {stats['successful_tests']}/{stats['total_tests']} ({stats['success_rate']:.1%})")
            if stats['successful_tests'] > 0:
                print(f"  Avg Processing Time: {stats['avg_processing_time']:.2f}s")
                print(f"  Avg Confidence: {stats['avg_confidence']:.1f}%")
                print(f"  Avg Text Length: {stats['avg_text_length']:.0f} characters")
        
        print(f"\nResults saved in 'results/' directory")
        print("- Text files: results/text_files/")
        print("- JSON files: results/json_files/")
        print("- Summary: results/ocr_summary.json")

def create_sample_image():
    """Create a simple sample image for testing"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple text image
        img = Image.new('RGB', (600, 150), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font
        try:
            font = ImageFont.truetype("arial.ttf", 30)
        except:
            font = ImageFont.load_default()
        
        text = "Hello World!\nThis is a simple OCR test."
        draw.text((20, 40), text, fill='black', font=font)
        
        sample_file = Path("test_images") / "sample_text.png"
        img.save(sample_file)
        print(f"✓ Created sample image: {sample_file}")
        
    except ImportError:
        print("⚠ PIL not available. Please add your own test images to test_images/ directory")

def main():
    """Main function"""
    print("="*60)
    print("SIMPLE OCR TESTING")
    print("="*60)
    
    # Setup
    setup_directories()
    create_sample_image()
    
    # Initialize OCR
    ocr = SimpleOCR()
    
    # Check if test images exist
    test_dir = Path("test_images")
    image_files = list(test_dir.glob("*.png")) + list(test_dir.glob("*.jpg")) + list(test_dir.glob("*.jpeg"))
    
    if not image_files:
        print("\nNo test images found!")
        print("Please add image files to the 'test_images' directory")
        print("Supported formats: PNG, JPG, JPEG, BMP, TIFF")
        return
    
    print(f"\nFound {len(image_files)} test images")
    
    # Process all images
    ocr.process_directory("test_images")
    
    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    main()
