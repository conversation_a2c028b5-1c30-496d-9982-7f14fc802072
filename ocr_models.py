"""
OCR Model Wrappers for standardized testing
"""
import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, <PERSON><PERSON>
import numpy as np
from PIL import Image
import cv2

logger = logging.getLogger(__name__)

class OCRModel(ABC):
    """Abstract base class for OCR models"""
    
    def __init__(self, name: str, config: Dict = None):
        self.name = name
        self.config = config or {}
        self.model = None
        self._initialize_model()
    
    @abstractmethod
    def _initialize_model(self):
        """Initialize the OCR model"""
        pass
    
    @abstractmethod
    def _extract_text(self, image: np.ndarray) -> Tuple[str, float]:
        """
        Extract text from image
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Tuple of (extracted_text, confidence_score)
        """
        pass
    
    def process_image(self, image_path: str) -> Dict[str, Any]:
        """
        Process an image and return OCR results
        
        Args:
            image_path: Path to the input image
            
        Returns:
            Dictionary containing OCR results and metadata
        """
        try:
            start_time = time.time()
            
            
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            
            extracted_text, confidence = self._extract_text(image)
            
            processing_time = time.time() - start_time
            
            result = {
                'model_name': self.name,
                'image_path': image_path,
                'extracted_text': extracted_text,
                'confidence_score': confidence,
                'processing_time': processing_time,
                'success': True,
                'error': None
            }
            
            logger.info(f"{self.name} processed {image_path} in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error in {self.name} processing {image_path}: {str(e)}")
            return {
                'model_name': self.name,
                'image_path': image_path,
                'extracted_text': '',
                'confidence_score': 0.0,
                'processing_time': 0.0,
                'success': False,
                'error': str(e)
            }

class TesseractOCR(OCRModel):
    """Tesseract OCR wrapper"""
    
    def _initialize_model(self):
        try:
            import pytesseract
            self.pytesseract = pytesseract
            logger.info("Tesseract OCR initialized successfully")
        except ImportError:
            logger.error("pytesseract not installed")
            raise
    
    def _extract_text(self, image: np.ndarray) -> Tuple[str, float]:
        config = self.config.get('config', '--oem 3 --psm 6')
        
        
        text = self.pytesseract.image_to_string(image, config=config)
        
        
        try:
            data = self.pytesseract.image_to_data(image, config=config, output_type=self.pytesseract.Output.DICT)
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        except:
            avg_confidence = 0
        
        return text.strip(), avg_confidence / 100.0

class EasyOCR(OCRModel):
    """EasyOCR wrapper"""
    
    def _initialize_model(self):
        try:
            import easyocr
            languages = self.config.get('languages', ['en'])
            gpu = self.config.get('gpu', True)
            self.reader = easyocr.Reader(languages, gpu=gpu)
            logger.info("EasyOCR initialized successfully")
        except ImportError:
            logger.error("easyocr not installed")
            raise
    
    def _extract_text(self, image: np.ndarray) -> Tuple[str, float]:
        results = self.reader.readtext(image)
        
        if not results:
            return "", 0.0
        
        
        texts = []
        confidences = []
        
        for (bbox, text, confidence) in results:
            texts.append(text)
            confidences.append(confidence)
        
        combined_text = ' '.join(texts)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return combined_text, avg_confidence

class TrOCR(OCRModel):
    """TrOCR (Transformer-based OCR) wrapper for handwritten text"""

    def _initialize_model(self):
        try:
            from transformers import TrOCRProcessor, VisionEncoderDecoderModel
            import torch

            model_name = self.config.get('model_name', 'microsoft/trocr-base-handwritten')
            device = self.config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')

            self.processor = TrOCRProcessor.from_pretrained(model_name)
            self.model = VisionEncoderDecoderModel.from_pretrained(model_name)
            self.model.to(device)
            self.device = device

            logger.info(f"TrOCR initialized successfully on {device}")
        except ImportError:
            logger.error("transformers or torch not installed")
            raise

    def _extract_text(self, image: np.ndarray) -> Tuple[str, float]:
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)

        
        pixel_values = self.processor(images=pil_image, return_tensors="pt").pixel_values
        pixel_values = pixel_values.to(self.device)

        
        generated_ids = self.model.generate(pixel_values)
        generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

        
        confidence = min(0.9, len(generated_text) / 100.0) if generated_text.strip() else 0.0

        return generated_text.strip(), confidence

def get_available_models(config: Dict) -> Dict[str, OCRModel]:
    """
    Get all available and enabled OCR models

    Args:
        config: Configuration dictionary with model settings

    Returns:
        Dictionary of model name to OCRModel instance
    """
    models = {}

    for model_name, model_config in config.items():
        if not model_config.get('enabled', False):
            continue

        try:
            if model_name == 'tesseract':
                models[model_name] = TesseractOCR(model_name, model_config)
            elif model_name == 'easyocr':
                models[model_name] = EasyOCR(model_name, model_config)
            elif model_name == 'paddleocr':
                models[model_name] = PaddleOCR(model_name, model_config)
            elif model_name == 'trocr':
                models[model_name] = TrOCR(model_name, model_config)
            else:
                logger.warning(f"Unknown model type: {model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize {model_name}: {str(e)}")

    return models

class PaddleOCR(OCRModel):
    """PaddleOCR wrapper"""
    
    def _initialize_model(self):
        try:
            from paddleocr import PaddleOCR
            lang = self.config.get('lang', 'en')
            use_gpu = self.config.get('use_gpu', True)
            self.ocr = PaddleOCR(use_angle_cls=True, lang=lang, use_gpu=use_gpu)
            logger.info("PaddleOCR initialized successfully")
        except ImportError:
            logger.error("paddleocr not installed")
            raise
    
    def _extract_text(self, image: np.ndarray) -> Tuple[str, float]:
        results = self.ocr.ocr(image, cls=True)
        
        if not results or not results[0]:
            return "", 0.0
        
        texts = []
        confidences = []
        
        for line in results[0]:
            if line:
                text = line[1][0]
                confidence = line[1][1]
                texts.append(text)
                confidences.append(confidence)
        
        combined_text = ' '.join(texts)
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        return combined_text, avg_confidence
